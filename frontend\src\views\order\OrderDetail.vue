<template>
  <div class="order-detail">
    <div class="page-header">
      <h2>订单详情</h2>
      <el-button @click="$router.back()">
        <el-icon><Back /></el-icon>
        返回
      </el-button>
    </div>
    
    <el-card>
      <div class="coming-soon">
        <el-icon class="icon"><View /></el-icon>
        <h3>订单详情功能</h3>
        <p>此功能将在第二部分实现，敬请期待...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Back, View } from '@element-plus/icons-vue'
</script>

<style scoped lang="scss">
.order-detail {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    h2 {
      margin: 0;
      color: #333;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 60px 20px;
    
    .icon {
      font-size: 4rem;
      color: #ccc;
      margin-bottom: 20px;
    }
    
    h3 {
      margin: 0 0 12px 0;
      color: #666;
    }
    
    p {
      margin: 0;
      color: #999;
    }
  }
}
</style>
