<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h2>控制面板</h2>
      <el-button type="primary" @click="refreshData">
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon today-orders">
          <el-icon><Document /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.todayOrders || 0 }}</div>
          <div class="stat-label">今日订单</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon pending-orders">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.pendingOrders || 0 }}</div>
          <div class="stat-label">待处理订单</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon today-revenue">
          <el-icon><Money /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ stats.todayRevenue || 0 }}</div>
          <div class="stat-label">今日销售额</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon total-dishes">
          <el-icon><Food /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalDishes || 0 }}</div>
          <div class="stat-label">菜品总数</div>
        </div>
      </div>
    </div>
    
    <!-- 快捷操作 -->
    <div class="quick-actions">
      <h3>快捷操作</h3>
      <div class="action-grid">
        <el-card class="action-card" @click="$router.push('/orders/create')">
          <div class="action-icon">
            <el-icon><Plus /></el-icon>
          </div>
          <div class="action-title">创建订单</div>
        </el-card>
        
        <el-card class="action-card" @click="$router.push('/dishes/add')">
          <div class="action-icon">
            <el-icon><CirclePlus /></el-icon>
          </div>
          <div class="action-title">添加菜品</div>
        </el-card>
        
        <el-card class="action-card" @click="$router.push('/orders')">
          <div class="action-icon">
            <el-icon><List /></el-icon>
          </div>
          <div class="action-title">订单管理</div>
        </el-card>
        
        <el-card class="action-card" @click="$router.push('/dishes')">
          <div class="action-icon">
            <el-icon><Menu /></el-icon>
          </div>
          <div class="action-title">菜品管理</div>
        </el-card>
      </div>
    </div>
    
    <!-- 欢迎信息 -->
    <div class="welcome-section">
      <el-card>
        <h3>欢迎使用酒店点单系统</h3>
        <p>您好，{{ authStore.user?.name }}！今天是 {{ currentDate }}，祝您工作愉快！</p>
        <div class="system-info">
          <el-tag type="success">系统运行正常</el-tag>
          <el-tag type="info">当前角色：{{ roleText }}</el-tag>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { orderApi } from '@/api/order'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Document,
  Clock,
  Money,
  Food,
  Plus,
  CirclePlus,
  List,
  Menu,
} from '@element-plus/icons-vue'

const authStore = useAuthStore()

const stats = ref({
  todayOrders: 0,
  pendingOrders: 0,
  todayRevenue: 0,
  totalDishes: 0,
})

const loading = ref(false)

// 当前日期
const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
  })
})

// 角色文本
const roleText = computed(() => {
  return authStore.user?.role === 'ADMIN' ? '管理员' : '服务员'
})

// 获取统计数据
const loadStats = async () => {
  try {
    loading.value = true
    const response = await orderApi.getTodayStats()
    if (response.data) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadStats()
  ElMessage.success('数据已刷新')
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped lang="scss">
.dashboard {
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    h2 {
      margin: 0;
      color: #333;
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
    
    .stat-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        
        &.today-orders {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        &.pending-orders {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        &.today-revenue {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        &.total-dishes {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }
      
      .stat-content {
        flex: 1;
        
        .stat-value {
          font-size: 2rem;
          font-weight: bold;
          color: #333;
          line-height: 1;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 0.9rem;
          color: #666;
        }
      }
    }
  }
  
  .quick-actions {
    margin-bottom: 32px;
    
    h3 {
      margin: 0 0 16px 0;
      color: #333;
    }
    
    .action-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      
      .action-card {
        cursor: pointer;
        transition: all 0.3s;
        text-align: center;
        padding: 24px;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        
        .action-icon {
          font-size: 2rem;
          color: #1890ff;
          margin-bottom: 12px;
        }
        
        .action-title {
          font-size: 1rem;
          font-weight: 500;
          color: #333;
        }
      }
    }
  }
  
  .welcome-section {
    h3 {
      margin: 0 0 12px 0;
      color: #333;
    }
    
    p {
      margin: 0 0 16px 0;
      color: #666;
      line-height: 1.6;
    }
    
    .system-info {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
  }
}

@media (max-width: 768px) {
  .dashboard {
    .dashboard-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
    
    .stats-grid {
      grid-template-columns: 1fr;
    }
    
    .action-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}
</style>
