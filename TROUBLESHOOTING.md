# 酒店点单系统 - 故障排除指南

## 常见问题及解决方案

### 1. 前端空白页面问题

#### 可能原因：
- 后端服务未启动
- 跨域配置问题
- 前端路由配置错误
- API请求失败

#### 解决步骤：

1. **检查后端服务**
   ```bash
   # 访问后端测试接口
   http://localhost:8080/test/hello
   ```
   如果无法访问，说明后端服务未启动或配置有问题。

2. **检查前端测试页面**
   ```bash
   # 访问前端测试页面
   http://localhost:3000/test
   ```
   这个页面可以帮助诊断前后端连接问题。

3. **检查浏览器控制台**
   - 打开浏览器开发者工具 (F12)
   - 查看Console标签页的错误信息
   - 查看Network标签页的网络请求状态

### 2. Sass警告问题

#### 警告信息：
```
Deprecation Warning [legacy-js-api]: The legacy JS API is deprecated
```

#### 解决方案：
已在 `vite.config.js` 中添加现代编译器配置：
```javascript
css: {
  preprocessorOptions: {
    scss: {
      api: 'modern-compiler'
    }
  }
}
```

### 3. 跨域问题

#### 症状：
- 前端无法访问后端API
- 浏览器控制台显示CORS错误

#### 解决方案：
1. 确保后端 `CorsConfig.java` 中的 `@Configuration` 注解未被注释
2. 检查 `application.yml` 中的跨域配置
3. 确保前端请求地址正确

### 4. 数据库连接问题

#### 症状：
- 后端启动失败
- 数据库相关错误

#### 解决方案：
1. 检查MySQL服务是否启动
2. 确认数据库连接配置：
   ```yaml
   spring:
     datasource:
       url: **********************************************
       username: root
       password: 123456
   ```
3. 创建数据库：
   ```sql
   CREATE DATABASE hotel_order_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

## 调试步骤

### 1. 后端调试

1. **启动后端服务**
   ```bash
   cd backend
   mvn spring-boot:run
   ```
   或使用提供的脚本：
   ```bash
   start-debug.bat
   ```

2. **测试后端接口**
   ```bash
   # 测试基本连接
   curl http://localhost:8080/test/hello
   
   # 测试健康检查
   curl http://localhost:8080/test/health
   
   # 测试系统信息
   curl http://localhost:8080/test/info
   ```

### 2. 前端调试

1. **启动前端服务**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```
   或使用提供的脚本：
   ```bash
   start-debug.bat
   ```

2. **访问测试页面**
   ```
   http://localhost:3000/test
   ```

3. **检查网络请求**
   - 打开浏览器开发者工具
   - 切换到Network标签
   - 刷新页面，观察API请求状态

## 环境要求

### 后端环境
- Java 17+
- Maven 3.6+
- MySQL 8.0+

### 前端环境
- Node.js 16+
- npm 8+

## 端口配置

- 后端服务：http://localhost:8080
- 前端服务：http://localhost:3000
- MySQL数据库：localhost:3306

## 日志查看

### 后端日志
```bash
# 查看应用日志
tail -f backend/logs/hotel-order-system.log

# 查看控制台输出
# 在启动后端时直接查看控制台
```

### 前端日志
```bash
# 查看浏览器控制台
# F12 -> Console标签页
```

## 联系支持

如果以上解决方案都无法解决问题，请提供以下信息：

1. 操作系统版本
2. Java版本 (`java -version`)
3. Node.js版本 (`node --version`)
4. 错误信息截图
5. 浏览器控制台错误信息
6. 后端启动日志

## 快速重置

如果问题严重，可以尝试完全重置：

1. **清理前端**
   ```bash
   cd frontend
   rm -rf node_modules
   rm package-lock.json
   npm install
   ```

2. **清理后端**
   ```bash
   cd backend
   mvn clean
   mvn compile
   ```

3. **重启服务**
   - 先启动后端服务
   - 再启动前端服务
   - 访问测试页面验证连接
