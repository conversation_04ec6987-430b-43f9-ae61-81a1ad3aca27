import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import { ElMessage } from 'element-plus'
import router from '@/router'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'ADMIN')
  const isStaff = computed(() => user.value?.role === 'STAFF')

  // 登录
  const login = async (loginData) => {
    try {
      isLoading.value = true
      const response = await authApi.login(loginData)
      
      if (response.data) {
        token.value = response.data.token
        user.value = response.data.user
        
        // 保存到本地存储
        localStorage.setItem('token', token.value)
        localStorage.setItem('user', JSON.stringify(user.value))
        
        ElMessage.success('登录成功')
        
        // 跳转到主页
        router.push('/')
        
        return response
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (registerData) => {
    try {
      isLoading.value = true
      const response = await authApi.register(registerData)
      
      ElMessage.success('注册成功，请登录')
      return response
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用后端登出接口
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      user.value = null
      token.value = ''
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      
      // 跳转到登录页
      router.push('/login')
      ElMessage.success('已退出登录')
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (savedToken && savedUser) {
      token.value = savedToken
      user.value = JSON.parse(savedUser)
      
      try {
        // 验证token是否有效
        const response = await authApi.getCurrentUser()
        user.value = response.data
        localStorage.setItem('user', JSON.stringify(user.value))
      } catch (error) {
        console.error('Token验证失败:', error)
        // Token无效，清除本地数据，但不显示消息和跳转
        logout(false)
      }
    }
  }

  // 更新用户信息
  const updateUser = (userData) => {
    user.value = { ...user.value, ...userData }
    localStorage.setItem('user', JSON.stringify(user.value))
  }

  // 修改密码
  const changePassword = async (passwordData) => {
    try {
      isLoading.value = true
      const response = await authApi.changePassword(passwordData)
      ElMessage.success('密码修改成功')
      return response
    } catch (error) {
      console.error('密码修改失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    isStaff,
    
    // 方法
    login,
    register,
    logout,
    checkAuth,
    updateUser,
    changePassword,
  }
})
