<template>
  <div class="login-container gradient-bg">
    <div class="login-card">
      <div class="login-header">
        <el-icon class="login-icon">
          <Restaurant />
        </el-icon>
        <h2>酒店点单系统</h2>
        <p>欢迎回来，请登录您的账户</p>
      </div>
      
      <div class="login-body">
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          size="large"
          @submit.prevent="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              prefix-icon="User"
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              show-password
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              style="width: 100%"
              :loading="authStore.isLoading"
              @click="handleLogin"
            >
              <el-icon><Right /></el-icon>
              登录
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="login-footer">
          还没有账户？
          <router-link to="/register" class="register-link">
            立即注册
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import { Restaurant, Right } from '@element-plus/icons-vue'

const authStore = useAuthStore()
const loginFormRef = ref()

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: '',
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 位', trigger: 'blur' },
  ],
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    await authStore.login(loginForm)
  } catch (error) {
    if (error.errors) {
      // 表单验证错误
      return
    }
    console.error('登录失败:', error)
  }
}
</script>

<style scoped lang="scss">
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 450px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.login-header {
  background: var(--primary);
  color: white;
  text-align: center;
  padding: 40px 30px;
  
  .login-icon {
    font-size: 3rem;
    margin-bottom: 15px;
  }
  
  h2 {
    font-size: 1.8rem;
    margin-bottom: 8px;
    font-weight: 600;
  }
  
  p {
    opacity: 0.9;
    font-size: 0.95rem;
  }
}

.login-body {
  padding: 40px 30px;
}

.login-footer {
  text-align: center;
  margin-top: 20px;
  color: var(--gray);
  font-size: 0.9rem;
  
  .register-link {
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

@media (max-width: 768px) {
  .login-container {
    padding: 10px;
  }
  
  .login-header {
    padding: 30px 20px;
  }
  
  .login-body {
    padding: 30px 20px;
  }
}
</style>
