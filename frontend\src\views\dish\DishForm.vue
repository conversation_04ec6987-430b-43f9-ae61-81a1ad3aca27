<template>
  <div class="dish-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑菜品' : '添加菜品' }}</h2>
      <el-button @click="$router.back()">
        <el-icon><Back /></el-icon>
        返回
      </el-button>
    </div>
    
    <el-card>
      <div class="coming-soon">
        <el-icon class="icon"><Edit /></el-icon>
        <h3>菜品表单功能</h3>
        <p>此功能将在第二部分实现，敬请期待...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { Back, Edit } from '@element-plus/icons-vue'

const route = useRoute()

const isEdit = computed(() => route.params.id)
</script>

<style scoped lang="scss">
.dish-form {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    h2 {
      margin: 0;
      color: #333;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 60px 20px;
    
    .icon {
      font-size: 4rem;
      color: #ccc;
      margin-bottom: 20px;
    }
    
    h3 {
      margin: 0 0 12px 0;
      color: #666;
    }
    
    p {
      margin: 0;
      color: #999;
    }
  }
}
</style>
