<template>
  <div style="padding: 50px; text-align: center; background: #f0f0f0; min-height: 100vh;">
    <h1 style="color: #333; font-size: 3rem;">🎉 Vue 3 正在工作！</h1>
    <p style="font-size: 1.5rem; color: #666;">如果你能看到这个页面，说明前端基础功能正常</p>
    
    <div style="margin: 30px 0;">
      <button 
        @click="count++" 
        style="padding: 15px 30px; font-size: 1.2rem; background: #409eff; color: white; border: none; border-radius: 5px; cursor: pointer;"
      >
        点击次数: {{ count }}
      </button>
    </div>
    
    <div style="margin: 30px 0;">
      <input 
        v-model="message" 
        placeholder="输入一些文字" 
        style="padding: 10px; font-size: 1rem; width: 300px; border: 1px solid #ddd; border-radius: 5px;"
      />
      <p style="margin-top: 10px; font-size: 1.1rem;">你输入的内容: <strong>{{ message }}</strong></p>
    </div>
    
    <div style="margin: 30px 0;">
      <button @click="goToWelcome" style="margin: 5px; padding: 10px 20px; background: #67c23a; color: white; border: none; border-radius: 5px; cursor: pointer;">
        欢迎页面
      </button>
      <button @click="goToLogin" style="margin: 5px; padding: 10px 20px; background: #e6a23c; color: white; border: none; border-radius: 5px; cursor: pointer;">
        登录页面
      </button>
      <button @click="goToTest" style="margin: 5px; padding: 10px 20px; background: #909399; color: white; border: none; border-radius: 5px; cursor: pointer;">
        完整测试
      </button>
    </div>
    
    <div style="margin-top: 50px; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
      <h3>系统信息</h3>
      <p>Vue版本: {{ vueVersion }}</p>
      <p>当前路径: {{ currentPath }}</p>
      <p>当前时间: {{ currentTime }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { version } from 'vue'

const router = useRouter()
const route = useRoute()

const count = ref(0)
const message = ref('Hello Vue 3!')
const currentTime = ref('')
const vueVersion = version

const currentPath = computed(() => route.path)

let timeInterval = null

const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

const goToWelcome = () => {
  router.push('/welcome')
}

const goToLogin = () => {
  router.push('/login')
}

const goToTest = () => {
  router.push('/test')
}

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
  console.log('SimpleTest 组件已挂载')
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
  console.log('SimpleTest 组件已卸载')
})
</script>
