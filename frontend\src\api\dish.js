import request from '@/utils/request'

/**
 * 菜品相关API
 */
export const dishApi = {
  /**
   * 分页查询菜品列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页大小
   * @param {string} params.name - 菜品名称
   * @param {string} params.category - 分类
   * @param {number} params.status - 状态
   */
  getDishPage(params) {
    return request({
      url: '/dishes',
      method: 'get',
      params,
    })
  },

  /**
   * 根据ID查询菜品
   * @param {number} id - 菜品ID
   */
  getDishById(id) {
    return request({
      url: `/dishes/${id}`,
      method: 'get',
    })
  },

  /**
   * 创建菜品
   * @param {Object} data - 菜品数据
   */
  createDish(data) {
    return request({
      url: '/dishes',
      method: 'post',
      data,
    })
  },

  /**
   * 更新菜品
   * @param {number} id - 菜品ID
   * @param {Object} data - 菜品数据
   */
  updateDish(id, data) {
    return request({
      url: `/dishes/${id}`,
      method: 'put',
      data,
    })
  },

  /**
   * 删除菜品
   * @param {number} id - 菜品ID
   */
  deleteDish(id) {
    return request({
      url: `/dishes/${id}`,
      method: 'delete',
    })
  },

  /**
   * 获取可用菜品列表
   */
  getAvailableDishes() {
    return request({
      url: '/dishes/available',
      method: 'get',
    })
  },

  /**
   * 根据分类获取菜品
   * @param {string} category - 分类
   */
  getDishesByCategory(category) {
    return request({
      url: '/dishes/category',
      method: 'get',
      params: { category },
    })
  },

  /**
   * 获取菜品分类统计
   */
  getCategoryStats() {
    return request({
      url: '/dishes/category-stats',
      method: 'get',
    })
  },

  /**
   * 获取热销菜品
   * @param {number} limit - 限制数量
   */
  getHotDishes(limit = 10) {
    return request({
      url: '/dishes/hot',
      method: 'get',
      params: { limit },
    })
  },

  /**
   * 上传菜品图片
   * @param {File} file - 图片文件
   */
  uploadDishImage(file) {
    const formData = new FormData()
    formData.append('file', file)
    
    return request({
      url: '/dishes/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
}
