# 酒店点单系统 - 前端

基于 Vue 3 + Element Plus 的现代化酒店点单系统前端应用。

## 技术栈

- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **UI 库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP 客户端**: Axios
- **样式**: SCSS
- **图标**: Element Plus Icons

## 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口
│   │   ├── auth.js        # 认证相关
│   │   ├── dish.js        # 菜品相关
│   │   └── order.js       # 订单相关
│   ├── components/        # 公共组件
│   │   └── ChangePasswordDialog.vue
│   ├── layout/            # 布局组件
│   │   └── MainLayout.vue
│   ├── router/            # 路由配置
│   │   └── index.js
│   ├── stores/            # 状态管理
│   │   ├── auth.js        # 认证状态
│   │   └── app.js         # 应用状态
│   ├── styles/            # 全局样式
│   │   └── index.scss
│   ├── utils/             # 工具函数
│   │   └── request.js     # HTTP 请求封装
│   ├── views/             # 页面组件
│   │   ├── auth/          # 认证页面
│   │   ├── dashboard/     # 仪表盘
│   │   ├── dish/          # 菜品管理
│   │   ├── order/         # 订单管理
│   │   ├── user/          # 用户管理
│   │   ├── report/        # 报表统计
│   │   ├── setting/       # 系统设置
│   │   └── error/         # 错误页面
│   ├── App.vue            # 根组件
│   └── main.js            # 应用入口
├── index.html             # HTML 模板
├── package.json           # 依赖配置
├── vite.config.js         # Vite 配置
└── README.md              # 项目说明
```

## 功能特性

### 第一部分 (已完成)

✅ **基础架构**
- Vue 3 项目初始化
- Vite 构建配置
- Element Plus UI 库集成
- 路由配置和守卫
- 状态管理 (Pinia)
- HTTP 请求封装

✅ **认证系统**
- 用户登录/注册
- JWT Token 管理
- 路由权限控制
- 用户状态管理
- 密码修改功能

✅ **布局系统**
- 响应式主布局
- 侧边栏导航
- 顶部导航栏
- 面包屑导航
- 主题切换

✅ **基础页面**
- 登录页面
- 注册页面
- 仪表盘页面
- 404 错误页面

### 第二部分 (待实现)

🔄 **业务功能**
- 菜品管理 (CRUD)
- 订单管理 (创建、查看、状态更新)
- 用户管理 (仅管理员)
- 报表统计
- 系统设置
- 通知系统

## 开发指南

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## API 接口

项目已配置代理，开发环境下 `/api` 请求会自动转发到 `http://localhost:8080`。

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/me` - 获取当前用户信息
- `POST /api/auth/change-password` - 修改密码
- `POST /api/auth/logout` - 用户登出

### 菜品接口
- `GET /api/dishes` - 分页查询菜品
- `GET /api/dishes/{id}` - 获取菜品详情
- `POST /api/dishes` - 创建菜品
- `PUT /api/dishes/{id}` - 更新菜品
- `DELETE /api/dishes/{id}` - 删除菜品
- `GET /api/dishes/available` - 获取可用菜品
- `GET /api/dishes/hot` - 获取热销菜品

### 订单接口
- `GET /api/orders` - 分页查询订单
- `GET /api/orders/{id}` - 获取订单详情
- `POST /api/orders` - 创建订单
- `PATCH /api/orders/{id}/status` - 更新订单状态
- `DELETE /api/orders/{id}` - 删除订单
- `GET /api/orders/today-stats` - 获取今日统计

## 设计原则

1. **组件化**: 采用组件化开发，提高代码复用性
2. **响应式**: 支持多种设备尺寸，提供良好的移动端体验
3. **类型安全**: 使用 TypeScript 提高代码质量 (后续版本)
4. **性能优化**: 路由懒加载、组件按需引入
5. **用户体验**: 统一的交互设计和视觉风格

## 注意事项

- 确保后端服务已启动 (http://localhost:8080)
- 开发环境下会自动处理跨域问题
- 生产环境需要配置 Nginx 反向代理
- 所有 API 请求都需要 JWT Token 认证 (除登录/注册接口)

## 下一步计划

第二部分将实现完整的业务功能，包括：
- 菜品管理的完整 CRUD 操作
- 订单管理的创建、查看、状态流转
- 数据统计和图表展示
- 用户权限管理
- 系统配置功能
