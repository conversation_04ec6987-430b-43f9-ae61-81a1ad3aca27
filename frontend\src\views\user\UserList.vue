<template>
  <div class="user-list">
    <div class="page-header">
      <h2>用户管理</h2>
      <el-button type="primary">
        <el-icon><Plus /></el-icon>
        添加用户
      </el-button>
    </div>
    
    <el-card>
      <div class="coming-soon">
        <el-icon class="icon"><User /></el-icon>
        <h3>用户管理功能</h3>
        <p>此功能将在第二部分实现，敬请期待...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Plus, User } from '@element-plus/icons-vue'
</script>

<style scoped lang="scss">
.user-list {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    h2 {
      margin: 0;
      color: #333;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 60px 20px;
    
    .icon {
      font-size: 4rem;
      color: #ccc;
      margin-bottom: 20px;
    }
    
    h3 {
      margin: 0 0 12px 0;
      color: #666;
    }
    
    p {
      margin: 0;
      color: #999;
    }
  }
}
</style>
