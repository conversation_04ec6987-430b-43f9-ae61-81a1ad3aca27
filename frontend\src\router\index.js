import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

// 路由配置
const routes = [
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/Test.vue'),
    meta: {
      title: '测试页面',
      requiresAuth: false,
    },
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
    },
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: {
      title: '注册',
      requiresAuth: false,
    },
  },
  {
    path: '/',
    component: () => import('@/layout/MainLayout.vue'),
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Dashboard.vue'),
        meta: {
          title: '控制面板',
          icon: 'House',
        },
      },
      {
        path: '/dishes',
        name: 'Dishes',
        component: () => import('@/views/dish/DishList.vue'),
        meta: {
          title: '菜品管理',
          icon: 'Food',
        },
      },
      {
        path: '/dishes/add',
        name: 'AddDish',
        component: () => import('@/views/dish/DishForm.vue'),
        meta: {
          title: '添加菜品',
          icon: 'Plus',
          hidden: true,
        },
      },
      {
        path: '/dishes/edit/:id',
        name: 'EditDish',
        component: () => import('@/views/dish/DishForm.vue'),
        meta: {
          title: '编辑菜品',
          icon: 'Edit',
          hidden: true,
        },
      },
      {
        path: '/orders',
        name: 'Orders',
        component: () => import('@/views/order/OrderList.vue'),
        meta: {
          title: '订单管理',
          icon: 'Document',
        },
      },
      {
        path: '/orders/create',
        name: 'CreateOrder',
        component: () => import('@/views/order/OrderForm.vue'),
        meta: {
          title: '创建订单',
          icon: 'Plus',
          hidden: true,
        },
      },
      {
        path: '/orders/:id',
        name: 'OrderDetail',
        component: () => import('@/views/order/OrderDetail.vue'),
        meta: {
          title: '订单详情',
          icon: 'View',
          hidden: true,
        },
      },
      {
        path: '/users',
        name: 'Users',
        component: () => import('@/views/user/UserList.vue'),
        meta: {
          title: '用户管理',
          icon: 'User',
          roles: ['ADMIN'], // 仅管理员可访问
        },
      },
      {
        path: '/reports',
        name: 'Reports',
        component: () => import('@/views/report/Reports.vue'),
        meta: {
          title: '报表统计',
          icon: 'DataAnalysis',
        },
      },
      {
        path: '/settings',
        name: 'Settings',
        component: () => import('@/views/setting/Settings.vue'),
        meta: {
          title: '系统设置',
          icon: 'Setting',
        },
      },
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/NotFound.vue'),
    meta: {
      title: '页面不存在',
    },
  },
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 酒店点单系统`
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      ElMessage.warning('请先登录')
      next('/login')
      return
    }
    
    // 检查角色权限
    if (to.meta.roles && !to.meta.roles.includes(authStore.user?.role)) {
      ElMessage.error('没有权限访问该页面')
      next('/')
      return
    }
  }
  
  // 已登录用户访问登录/注册页面，重定向到首页
  if ((to.name === 'Login' || to.name === 'Register') && authStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
})

export default router
