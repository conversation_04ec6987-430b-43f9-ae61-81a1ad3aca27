import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 侧边栏状态
  const sidebarCollapsed = ref(false)
  
  // 当前页面标题
  const pageTitle = ref('控制面板')
  
  // 面包屑导航
  const breadcrumbs = ref([
    { title: '首页', path: '/' }
  ])
  
  // 加载状态
  const globalLoading = ref(false)
  
  // 主题模式
  const isDark = ref(false)
  
  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
  
  // 设置页面标题
  const setPageTitle = (title) => {
    pageTitle.value = title
    document.title = `${title} - 酒店点单系统`
  }
  
  // 设置面包屑
  const setBreadcrumbs = (crumbs) => {
    breadcrumbs.value = crumbs
  }
  
  // 设置全局加载状态
  const setGlobalLoading = (loading) => {
    globalLoading.value = loading
  }
  
  // 切换主题
  const toggleTheme = () => {
    isDark.value = !isDark.value
    document.documentElement.classList.toggle('dark', isDark.value)
    localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
  }
  
  // 初始化主题
  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      isDark.value = savedTheme === 'dark'
      document.documentElement.classList.toggle('dark', isDark.value)
    }
  }
  
  return {
    // 状态
    sidebarCollapsed,
    pageTitle,
    breadcrumbs,
    globalLoading,
    isDark,
    
    // 方法
    toggleSidebar,
    setPageTitle,
    setBreadcrumbs,
    setGlobalLoading,
    toggleTheme,
    initTheme,
  }
})
