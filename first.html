<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>酒店点单系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #3498db;
            --primary-dark: #2980b9;
            --secondary: #e74c3c;
            --success: #2ecc71;
            --warning: #f39c12;
            --light: #f8f9fa;
            --dark: #343a40;
            --gray: #6c757d;
            --light-gray: #e9ecef;
            --border: #dee2e6;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
            color: var(--dark);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        header {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 15px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo i {
            font-size: 2rem;
            color: var(--primary);
        }
        
        .logo h1 {
            font-size: 1.8rem;
            color: var(--primary);
        }
        
        nav ul {
            display: flex;
            list-style: none;
            gap: 20px;
        }
        
        nav a {
            text-decoration: none;
            color: var(--gray);
            font-weight: 500;
            padding: 8px 15px;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        nav a:hover, nav a.active {
            background: var(--primary);
            color: white;
        }
        
        .user-controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 20px;
            border: none;
            border-radius: 5px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: var(--primary);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
        }
        
        .btn-outline {
            background: transparent;
            border: 1px solid var(--primary);
            color: var(--primary);
        }
        
        .btn-outline:hover {
            background: var(--primary);
            color: white;
        }
        
        .btn-danger {
            background: var(--secondary);
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 280px 1fr;
            gap: 25px;
        }
        
        .sidebar {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 25px;
            height: fit-content;
        }
        
        .sidebar h2 {
            margin-bottom: 20px;
            color: var(--primary);
            padding-bottom: 10px;
            border-bottom: 2px solid var(--light-gray);
        }
        
        .sidebar-menu {
            list-style: none;
        }
        
        .sidebar-menu li {
            margin-bottom: 15px;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            color: var(--dark);
            padding: 10px 15px;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background: var(--primary);
            color: white;
        }
        
        .content {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .content-header h2 {
            color: var(--primary);
        }
        
        .content-header .actions {
            display: flex;
            gap: 15px;
        }
        
        .card-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s, box-shadow 0.3s;
            border: 1px solid var(--border);
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }
        
        .card-img {
            height: 180px;
            background-size: cover;
            background-position: center;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .card-title {
            font-size: 1.3rem;
            margin-bottom: 10px;
            color: var(--dark);
        }
        
        .card-text {
            color: var(--gray);
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: var(--light);
            border-top: 1px solid var(--border);
        }
        
        .price {
            font-size: 1.4rem;
            font-weight: bold;
            color: var(--primary);
        }
        
        .category {
            display: inline-block;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            background: var(--light);
        }
        
        .category-main {
            background: #d4edda;
            color: #155724;
        }
        
        .category-drink {
            background: #cce5ff;
            color: #004085;
        }
        
        .category-dessert {
            background: #fff3cd;
            color: #856404;
        }
        
        .category-appetizer {
            background: #f8d7da;
            color: #721c24;
        }
        
        .stats-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
            text-align: center;
            border-left: 4px solid var(--primary);
        }
        
        .stat-card h3 {
            font-size: 1.1rem;
            color: var(--gray);
            margin-bottom: 10px;
        }
        
        .stat-card .value {
            font-size: 2.2rem;
            font-weight: bold;
            color: var(--primary);
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border);
        }
        
        th {
            background: var(--light);
            font-weight: 600;
            color: var(--gray);
        }
        
        tr:hover {
            background: rgba(52, 152, 219, 0.05);
        }
        
        .status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        .status-pending {
            background: #ffeeba;
            color: #856404;
        }
        
        .status-preparing {
            background: #b8daff;
            color: #004085;
        }
        
        .status-completed {
            background: #c3e6cb;
            color: #155724;
        }
        
        .status-cancelled {
            background: #f5c6cb;
            color: #721c24;
        }
        
        .auth-container {
            max-width: 500px;
            margin: 50px auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }
        
        .auth-header {
            background: var(--primary);
            color: white;
            text-align: center;
            padding: 30px;
        }
        
        .auth-header h2 {
            font-size: 1.8rem;
        }
        
        .auth-body {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark);
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid var(--border);
            border-radius: 5px;
            font-size: 1rem;
            transition: border 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        .footer a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .hidden {
            display: none;
        }
        
        @media (max-width: 992px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            header {
                flex-direction: column;
                gap: 15px;
            }
            
            nav ul {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .stats-container {
                grid-template-columns: 1fr;
            }
            
            .card-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 登录页面 -->
        <div id="login-page">
            <div class="auth-container">
                <div class="auth-header">
                    <h2><i class="fas fa-hotel"></i> 酒店点单系统</h2>
                    <p>欢迎回来，请登录您的账户</p>
                </div>
                <div class="auth-body">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" class="form-control" placeholder="请输入用户名">
                    </div>
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" class="form-control" placeholder="请输入密码">
                    </div>
                    <button id="login-btn" class="btn btn-primary" style="width: 100%;" onclick="login()">
                        <i class="fas fa-sign-in-alt"></i> 登录
                    </button>
                    <div class="footer">
                        还没有账户？ <a href="#" onclick="showPage('register-page')">立即注册</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 注册页面 -->
        <div id="register-page" class="hidden">
            <div class="auth-container">
                <div class="auth-header" style="background: #2ecc71;">
                    <h2><i class="fas fa-user-plus"></i> 创建新账户</h2>
                    <p>加入我们的酒店点单系统</p>
                </div>
                <div class="auth-body">
                    <div class="form-group">
                        <label for="reg-name">姓名</label>
                        <input type="text" id="reg-name" class="form-control" placeholder="请输入您的姓名">
                    </div>
                    <div class="form-group">
                        <label for="reg-username">用户名</label>
                        <input type="text" id="reg-username" class="form-control" placeholder="创建用户名">
                    </div>
                    <div class="form-group">
                        <label for="reg-password">密码</label>
                        <input type="password" id="reg-password" class="form-control" placeholder="设置密码">
                    </div>
                    <div class="form-group">
                        <label for="reg-role">角色</label>
                        <select id="reg-role" class="form-control">
                            <option value="STAFF">服务员</option>
                            <option value="ADMIN">管理员</option>
                        </select>
                    </div>
                    <button id="register-btn" class="btn btn-primary" style="width: 100%; background: #2ecc71;" onclick="register()">
                        <i class="fas fa-user-plus"></i> 注册
                    </button>
                    <div class="footer">
                        已有账户？ <a href="#" onclick="showPage('login-page')">立即登录</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主应用页面 -->
        <div id="main-app" class="hidden">
            <header>
                <div class="logo">
                    <i class="fas fa-utensils"></i>
                    <h1>酒店点单系统</h1>
                </div>
                <nav>
                    <ul>
                        <li><a href="#" class="active" onclick="showSection('dashboard')">首页</a></li>
                        <li><a href="#" onclick="showSection('dishes')">菜品管理</a></li>
                        <li><a href="#" onclick="showSection('orders')">订单管理</a></li>
                        <li><a href="#" onclick="showSection('reports')">报表统计</a></li>
                        <li><a href="#" onclick="showSection('settings')">系统设置</a></li>
                    </ul>
                </nav>
                <div class="user-controls">
                    <span><i class="fas fa-user"></i> 管理员</span>
                    <button class="btn btn-outline" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i> 退出
                    </button>
                </div>
            </header>
            
            <div class="main-content">
                <aside class="sidebar">
                    <h2><i class="fas fa-tachometer-alt"></i> 快捷菜单</h2>
                    <ul class="sidebar-menu">
                        <li><a href="#" class="active"><i class="fas fa-home"></i> 控制面板</a></li>
                        <li><a href="#" onclick="showSection('add-dish')"><i class="fas fa-plus-circle"></i> 添加菜品</a></li>
                        <li><a href="#" onclick="showSection('create-order')"><i class="fas fa-file-invoice"></i> 创建订单</a></li>
                        <li><a href="#"><i class="fas fa-history"></i> 订单历史</a></li>
                        <li><a href="#"><i class="fas fa-chart-bar"></i> 销售统计</a></li>
                        <li><a href="#"><i class="fas fa-users"></i> 员工管理</a></li>
                    </ul>
                    
                    <h2 style="margin-top: 30px;"><i class="fas fa-bell"></i> 通知</h2>
                    <div style="background: #fff8e1; padding: 15px; border-radius: 8px; margin-top: 15px;">
                        <p><i class="fas fa-exclamation-circle" style="color: #ff9800;"></i> 您有5个待处理订单</p>
                        <p><i class="fas fa-info-circle" style="color: #2196f3;"></i> 系统将于今晚12点进行维护</p>
                    </div>
                </aside>
                
                <main class="content">
                    <!-- 仪表盘 -->
                    <section id="dashboard-section">
                        <div class="content-header">
                            <h2><i class="fas fa-tachometer-alt"></i> 控制面板</h2>
                            <div class="actions">
                                <button class="btn btn-outline">
                                    <i class="fas fa-sync-alt"></i> 刷新
                                </button>
                            </div>
                        </div>
                        
                        <div class="stats-container">
                            <div class="stat-card">
                                <h3>今日订单</h3>
                                <div class="value">42</div>
                            </div>
                            <div class="stat-card">
                                <h3>待处理订单</h3>
                                <div class="value">8</div>
                            </div>
                            <div class="stat-card">
                                <h3>今日销售额</h3>
                                <div class="value">¥2,850</div>
                            </div>
                            <div class="stat-card">
                                <h3>菜品总数</h3>
                                <div class="value">38</div>
                            </div>
                        </div>
                        
                        <div class="content-header">
                            <h2><i class="fas fa-fire"></i> 热销菜品</h2>
                        </div>
                        
                        <div class="card-container">
                            <div class="card">
                                <div class="card-img" style="background: linear-gradient(rgba(0,0,0,0.2), rgba(0,0,0,0.2)), url('https://images.unsplash.com/photo-1563245372-f21724e3856d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80');"></div>
                                <div class="card-body">
                                    <h3 class="card-title">宫保鸡丁</h3>
                                    <p class="card-text">经典川菜，麻辣鲜香，配以花生米和青椒，色香味俱全。</p>
                                    <div>
                                        <span class="category category-main">主菜</span>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="price">¥38.00</div>
                                    <div>销量: 128份</div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-img" style="background: linear-gradient(rgba(0,0,0,0.2), rgba(0,0,0,0.2)), url('https://images.unsplash.com/photo-1606755456206-b25206cde27e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80');"></div>
                                <div class="card-body">
                                    <h3 class="card-title">清蒸鲈鱼</h3>
                                    <p class="card-text">鲜嫩多汁，清淡可口，保留鱼肉原汁原味。</p>
                                    <div>
                                        <span class="category category-main">主菜</span>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="price">¥68.00</div>
                                    <div>销量: 86份</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="content-header" style="margin-top: 30px;">
                            <h2><i class="fas fa-list"></i> 最近订单</h2>
                        </div>
                        
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>订单号</th>
                                        <th>桌号</th>
                                        <th>服务员</th>
                                        <th>金额</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>#ORD-2023078</td>
                                        <td>VIP3</td>
                                        <td>服务员A</td>
                                        <td>¥106.00</td>
                                        <td><span class="status status-completed">已完成</span></td>
                                        <td>
                                            <button class="btn btn-outline">查看</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#ORD-2023077</td>
                                        <td>A12</td>
                                        <td>服务员B</td>
                                        <td>¥76.00</td>
                                        <td><span class="status status-preparing">准备中</span></td>
                                        <td>
                                            <button class="btn btn-outline">查看</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#ORD-2023076</td>
                                        <td>B05</td>
                                        <td>服务员A</td>
                                        <td>¥142.00</td>
                                        <td><span class="status status-pending">待处理</span></td>
                                        <td>
                                            <button class="btn btn-outline">查看</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </section>
                    
                    <!-- 菜品管理 -->
                    <section id="dishes-section" class="hidden">
                        <div class="content-header">
                            <h2><i class="fas fa-utensils"></i> 菜品管理</h2>
                            <div class="actions">
                                <button class="btn btn-primary" onclick="showSection('add-dish')">
                                    <i class="fas fa-plus"></i> 添加菜品
                                </button>
                            </div>
                        </div>
                        
                        <div class="card-container">
                            <div class="card">
                                <div class="card-img" style="background: linear-gradient(rgba(0,0,0,0.2), rgba(0,0,0,0.2)), url('https://images.unsplash.com/photo-1563245372-f21724e3856d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80');"></div>
                                <div class="card-body">
                                    <h3 class="card-title">宫保鸡丁</h3>
                                    <p class="card-text">经典川菜，麻辣鲜香，配以花生米和青椒，色香味俱全。</p>
                                    <div>
                                        <span class="category category-main">主菜</span>
                                        <span style="color: #28a745; margin-left: 10px;">
                                            <i class="fas fa-check-circle"></i> 上架中
                                        </span>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="price">¥38.00</div>
                                    <div>
                                        <button class="btn btn-outline"><i class="fas fa-edit"></i> 编辑</button>
                                        <button class="btn btn-danger"><i class="fas fa-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-img" style="background: linear-gradient(rgba(0,0,0,0.2), rgba(0,0,0,0.2)), url('https://images.unsplash.com/photo-1606755456206-b25206cde27e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80');"></div>
                                <div class="card-body">
                                    <h3 class="card-title">清蒸鲈鱼</h3>
                                    <p class="card-text">鲜嫩多汁，清淡可口，保留鱼肉原汁原味。</p>
                                    <div>
                                        <span class="category category-main">主菜</span>
                                        <span style="color: #28a745; margin-left: 10px;">
                                            <i class="fas fa-check-circle"></i> 上架中
                                        </span>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="price">¥68.00</div>
                                    <div>
                                        <button class="btn btn-outline"><i class="fas fa-edit"></i> 编辑</button>
                                        <button class="btn btn-danger"><i class="fas fa-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-img" style="background: linear-gradient(rgba(0,0,0,0.2), rgba(0,0,0,0.2)), url('https://images.unsplash.com/photo-1505253716362-afaea1d3d1af?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80');"></div>
                                <div class="card-body">
                                    <h3 class="card-title">水果沙拉</h3>
                                    <p class="card-text">新鲜时令水果，搭配特制酸奶酱，健康美味。</p>
                                    <div>
                                        <span class="category category-dessert">甜点</span>
                                        <span style="color: #28a745; margin-left: 10px;">
                                            <i class="fas fa-check-circle"></i> 上架中
                                        </span>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="price">¥28.00</div>
                                    <div>
                                        <button class="btn btn-outline"><i class="fas fa-edit"></i> 编辑</button>
                                        <button class="btn btn-danger"><i class="fas fa-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-img" style="background: linear-gradient(rgba(0,0,0,0.2), rgba(0,0,0,0.2)), url('https://images.unsplash.com/photo-1596803244618-8dbee441d70b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80');"></div>
                                <div class="card-body">
                                    <h3 class="card-title">可乐</h3>
                                    <p class="card-text">冰镇可口可乐，畅爽解渴。</p>
                                    <div>
                                        <span class="category category-drink">饮品</span>
                                        <span style="color: #28a745; margin-left: 10px;">
                                            <i class="fas fa-check-circle"></i> 上架中
                                        </span>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="price">¥10.00</div>
                                    <div>
                                        <button class="btn btn-outline"><i class="fas fa-edit"></i> 编辑</button>
                                        <button class="btn btn-danger"><i class="fas fa-trash"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- 添加菜品 -->
                    <section id="add-dish-section" class="hidden">
                        <div class="content-header">
                            <h2><i class="fas fa-plus-circle"></i> 添加菜品</h2>
                        </div>
                        
                        <div style="max-width: 700px; margin: 0 auto;">
                            <div class="form-group">
                                <label for="dish-name">菜品名称</label>
                                <input type="text" id="dish-name" class="form-control" placeholder="请输入菜品名称">
                            </div>
                            
                            <div class="form-group">
                                <label for="dish-desc">菜品描述</label>
                                <textarea id="dish-desc" class="form-control" rows="3" placeholder="请输入菜品描述"></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="dish-price">价格 (¥)</label>
                                <input type="number" id="dish-price" class="form-control" placeholder="请输入价格">
                            </div>
                            
                            <div class="form-group">
                                <label for="dish-category">菜品分类</label>
                                <select id="dish-category" class="form-control">
                                    <option value="APPETIZER">开胃菜</option>
                                    <option value="MAIN">主菜</option>
                                    <option value="DESSERT">甜点</option>
                                    <option value="DRINK">饮品</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="dish-image">菜品图片</label>
                                <input type="file" id="dish-image" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label>
                                    <input type="checkbox"> 立即上架
                                </label>
                            </div>
                            
                            <div style="display: flex; gap: 15px; margin-top: 30px;">
                                <button class="btn btn-primary">
                                    <i class="fas fa-save"></i> 保存菜品
                                </button>
                                <button class="btn btn-outline" onclick="showSection('dishes')">
                                    <i class="fas fa-times"></i> 取消
                                </button>
                            </div>
                        </div>
                    </section>
                    
                    <!-- 订单管理 -->
                    <section id="orders-section" class="hidden">
                        <div class="content-header">
                            <h2><i class="fas fa-file-invoice"></i> 订单管理</h2>
                            <div class="actions">
                                <button class="btn btn-primary" onclick="showSection('create-order')">
                                    <i class="fas fa-plus"></i> 创建订单
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>订单号</th>
                                        <th>桌号</th>
                                        <th>服务员</th>
                                        <th>金额</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>#ORD-2023078</td>
                                        <td>VIP3</td>
                                        <td>服务员A</td>
                                        <td>¥106.00</td>
                                        <td><span class="status status-completed">已完成</span></td>
                                        <td>2023-07-15 14:30</td>
                                        <td>
                                            <button class="btn btn-outline">查看</button>
                                            <button class="btn btn-danger"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#ORD-2023077</td>
                                        <td>A12</td>
                                        <td>服务员B</td>
                                        <td>¥76.00</td>
                                        <td><span class="status status-preparing">准备中</span></td>
                                        <td>2023-07-15 13:45</td>
                                        <td>
                                            <button class="btn btn-outline">查看</button>
                                            <button class="btn btn-danger"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#ORD-2023076</td>
                                        <td>B05</td>
                                        <td>服务员A</td>
                                        <td>¥142.00</td>
                                        <td><span class="status status-pending">待处理</span></td>
                                        <td>2023-07-15 12:20</td>
                                        <td>
                                            <button class="btn btn-outline">查看</button>
                                            <button class="btn btn-danger"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>#ORD-2023075</td>
                                        <td>C08</td>
                                        <td>服务员C</td>
                                        <td>¥95.00</td>
                                        <td><span class="status status-cancelled">已取消</span></td>
                                        <td>2023-07-14 19:15</td>
                                        <td>
                                            <button class="btn btn-outline">查看</button>
                                            <button class="btn btn-danger"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </section>
                    
                    <!-- 其他部分省略 -->
                </main>
            </div>
        </div>
    </div>
    
    <script>
        // ==================== API Service Layer ====================
        class ApiService {
            constructor() {
                this.baseURL = 'http://localhost:8080/api';
                this.token = localStorage.getItem('token');
            }

            // 设置认证头
            getHeaders() {
                const headers = {
                    'Content-Type': 'application/json'
                };
                if (this.token) {
                    headers['Authorization'] = `Bearer ${this.token}`;
                }
                return headers;
            }

            // 通用请求方法
            async request(url, options = {}) {
                try {
                    const response = await fetch(`${this.baseURL}${url}`, {
                        ...options,
                        headers: {
                            ...this.getHeaders(),
                            ...options.headers
                        }
                    });

                    const data = await response.json();

                    if (!response.ok) {
                        throw new Error(data.message || '请求失败');
                    }

                    return data;
                } catch (error) {
                    console.error('API请求错误:', error);
                    throw error;
                }
            }

            // 认证相关API
            async login(username, password) {
                const response = await this.request('/auth/login', {
                    method: 'POST',
                    body: JSON.stringify({ username, password })
                });

                if (response.success && response.data.token) {
                    this.token = response.data.token;
                    localStorage.setItem('token', this.token);
                    localStorage.setItem('user', JSON.stringify(response.data.user));
                }

                return response;
            }

            async register(userData) {
                return await this.request('/auth/register', {
                    method: 'POST',
                    body: JSON.stringify(userData)
                });
            }

            async getCurrentUser() {
                return await this.request('/auth/me');
            }

            async logout() {
                try {
                    await this.request('/auth/logout', { method: 'POST' });
                } finally {
                    this.token = null;
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                }
            }

            // 菜品相关API
            async getDishes(params = {}) {
                const queryString = new URLSearchParams(params).toString();
                return await this.request(`/dishes?${queryString}`);
            }

            async getDishById(id) {
                return await this.request(`/dishes/${id}`);
            }

            async createDish(dishData) {
                return await this.request('/dishes', {
                    method: 'POST',
                    body: JSON.stringify(dishData)
                });
            }

            async updateDish(id, dishData) {
                return await this.request(`/dishes/${id}`, {
                    method: 'PUT',
                    body: JSON.stringify(dishData)
                });
            }

            async deleteDish(id) {
                return await this.request(`/dishes/${id}`, {
                    method: 'DELETE'
                });
            }

            async getAvailableDishes() {
                return await this.request('/dishes/available');
            }

            async getHotDishes(limit = 10) {
                return await this.request(`/dishes/hot?limit=${limit}`);
            }

            // 订单相关API
            async getOrders(params = {}) {
                const queryString = new URLSearchParams(params).toString();
                return await this.request(`/orders?${queryString}`);
            }

            async getOrderById(id) {
                return await this.request(`/orders/${id}`);
            }

            async createOrder(orderData) {
                return await this.request('/orders', {
                    method: 'POST',
                    body: JSON.stringify(orderData)
                });
            }

            async updateOrderStatus(id, status) {
                return await this.request(`/orders/${id}/status`, {
                    method: 'PATCH',
                    body: JSON.stringify({ status })
                });
            }

            async deleteOrder(id) {
                return await this.request(`/orders/${id}`, {
                    method: 'DELETE'
                });
            }

            async getTodayStats() {
                return await this.request('/orders/today-stats');
            }

            // 用户管理API (仅管理员)
            async getUsers(params = {}) {
                const queryString = new URLSearchParams(params).toString();
                return await this.request(`/users?${queryString}`);
            }

            async createUser(userData) {
                return await this.request('/users', {
                    method: 'POST',
                    body: JSON.stringify(userData)
                });
            }

            async updateUser(id, userData) {
                return await this.request(`/users/${id}`, {
                    method: 'PUT',
                    body: JSON.stringify(userData)
                });
            }

            async deleteUser(id) {
                return await this.request(`/users/${id}`, {
                    method: 'DELETE'
                });
            }
        }

        // ==================== State Management ====================
        class AppState {
            constructor() {
                this.user = null;
                this.isAuthenticated = false;
                this.currentPage = 'login-page';
                this.currentSection = 'dashboard-section';
                this.dishes = [];
                this.orders = [];
                this.stats = {};
                this.init();
            }

            init() {
                // 检查本地存储的用户信息
                const token = localStorage.getItem('token');
                const userData = localStorage.getItem('user');

                if (token && userData) {
                    this.user = JSON.parse(userData);
                    this.isAuthenticated = true;
                    this.currentPage = 'main-app';
                }
            }

            setUser(user) {
                this.user = user;
                this.isAuthenticated = true;
            }

            clearUser() {
                this.user = null;
                this.isAuthenticated = false;
                this.currentPage = 'login-page';
            }

            setCurrentPage(page) {
                this.currentPage = page;
            }

            setCurrentSection(section) {
                this.currentSection = section;
            }
        }

        // ==================== Application Controller ====================
        class HotelApp {
            constructor() {
                this.api = new ApiService();
                this.state = new AppState();
                this.init();
            }

            init() {
                // 初始化页面
                if (this.state.isAuthenticated) {
                    this.showPage('main-app');
                    this.loadDashboardData();
                } else {
                    this.showPage('login-page');
                }
            }

            // 页面切换逻辑
            showPage(pageId) {
                document.getElementById('login-page').classList.add('hidden');
                document.getElementById('register-page').classList.add('hidden');
                document.getElementById('main-app').classList.add('hidden');

                document.getElementById(pageId).classList.remove('hidden');
                this.state.setCurrentPage(pageId);
            }

            // 主应用内部分切换
            showSection(sectionId) {
                const sections = ['dashboard-section', 'dishes-section', 'add-dish-section', 'orders-section', 'users-section', 'create-order-section'];
                sections.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.classList.add('hidden');
                    }
                });

                const targetSection = document.getElementById(sectionId + '-section');
                if (targetSection) {
                    targetSection.classList.remove('hidden');
                    this.state.setCurrentSection(sectionId + '-section');
                }

                // 更新导航状态
                this.updateNavigation(sectionId);
            }

            updateNavigation(activeSection) {
                // 更新主导航
                document.querySelectorAll('nav a').forEach(link => {
                    link.classList.remove('active');
                });

                // 更新侧边栏导航
                document.querySelectorAll('.sidebar-menu a').forEach(link => {
                    link.classList.remove('active');
                });
            }

            // ==================== Authentication Functions ====================
            async login() {
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();

                if (!username || !password) {
                    this.showMessage('请输入用户名和密码', 'error');
                    return;
                }

                try {
                    this.showLoading('login-btn', true);
                    const response = await this.api.login(username, password);

                    if (response.success) {
                        this.state.setUser(response.data.user);
                        this.showMessage('登录成功', 'success');
                        this.showPage('main-app');
                        this.updateUserInfo();
                        await this.loadDashboardData();
                    } else {
                        this.showMessage(response.message || '登录失败', 'error');
                    }
                } catch (error) {
                    this.showMessage(error.message || '登录失败，请检查网络连接', 'error');
                } finally {
                    this.showLoading('login-btn', false);
                }
            }

            async register() {
                const name = document.getElementById('reg-name').value.trim();
                const username = document.getElementById('reg-username').value.trim();
                const password = document.getElementById('reg-password').value.trim();
                const role = document.getElementById('reg-role').value;

                if (!name || !username || !password) {
                    this.showMessage('请填写完整信息', 'error');
                    return;
                }

                if (password.length < 6) {
                    this.showMessage('密码长度不能少于6位', 'error');
                    return;
                }

                try {
                    this.showLoading('register-btn', true);
                    const response = await this.api.register({ name, username, password, role });

                    if (response.success) {
                        this.showMessage('注册成功！请登录', 'success');
                        this.showPage('login-page');
                        // 清空注册表单
                        document.getElementById('reg-name').value = '';
                        document.getElementById('reg-username').value = '';
                        document.getElementById('reg-password').value = '';
                    } else {
                        this.showMessage(response.message || '注册失败', 'error');
                    }
                } catch (error) {
                    this.showMessage(error.message || '注册失败，请检查网络连接', 'error');
                } finally {
                    this.showLoading('register-btn', false);
                }
            }

            async logout() {
                try {
                    await this.api.logout();
                } catch (error) {
                    console.error('退出登录错误:', error);
                } finally {
                    this.state.clearUser();
                    this.showPage('login-page');
                    // 清空表单
                    document.getElementById('username').value = '';
                    document.getElementById('password').value = '';
                }
            }

            // ==================== UI Helper Functions ====================
            showMessage(message, type = 'info') {
                // 创建消息提示
                const messageDiv = document.createElement('div');
                messageDiv.className = `message message-${type}`;
                messageDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 15px 20px;
                    border-radius: 5px;
                    color: white;
                    font-weight: 500;
                    z-index: 10000;
                    max-width: 300px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                `;

                // 设置不同类型的背景色
                switch (type) {
                    case 'success':
                        messageDiv.style.backgroundColor = '#2ecc71';
                        break;
                    case 'error':
                        messageDiv.style.backgroundColor = '#e74c3c';
                        break;
                    case 'warning':
                        messageDiv.style.backgroundColor = '#f39c12';
                        break;
                    default:
                        messageDiv.style.backgroundColor = '#3498db';
                }

                messageDiv.textContent = message;
                document.body.appendChild(messageDiv);

                // 3秒后自动移除
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 3000);
            }

            showLoading(buttonId, isLoading) {
                const button = document.getElementById(buttonId);
                if (!button) return;

                if (isLoading) {
                    button.disabled = true;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                } else {
                    button.disabled = false;
                    // 恢复原始文本
                    if (buttonId === 'login-btn') {
                        button.innerHTML = '<i class="fas fa-sign-in-alt"></i> 登录';
                    } else if (buttonId === 'register-btn') {
                        button.innerHTML = '<i class="fas fa-user-plus"></i> 注册';
                    }
                }
            }

            updateUserInfo() {
                if (this.state.user) {
                    const userSpan = document.querySelector('.user-controls span');
                    if (userSpan) {
                        userSpan.innerHTML = `<i class="fas fa-user"></i> ${this.state.user.name} (${this.state.user.role === 'ADMIN' ? '管理员' : '服务员'})`;
                    }
                }
            }

            // ==================== Data Loading Functions ====================
            async loadDashboardData() {
                try {
                    // 加载统计数据
                    const statsResponse = await this.api.getTodayStats();
                    if (statsResponse.success) {
                        this.updateStatsDisplay(statsResponse.data);
                    }

                    // 加载热销菜品
                    const hotDishesResponse = await this.api.getHotDishes(6);
                    if (hotDishesResponse.success) {
                        this.updateHotDishesDisplay(hotDishesResponse.data);
                    }

                    // 加载最近订单
                    const ordersResponse = await this.api.getOrders({ page: 1, size: 5 });
                    if (ordersResponse.success) {
                        this.updateRecentOrdersDisplay(ordersResponse.data.records);
                    }
                } catch (error) {
                    console.error('加载仪表盘数据失败:', error);
                    this.showMessage('加载数据失败', 'error');
                }
            }

            updateStatsDisplay(stats) {
                // 更新统计卡片
                const statCards = document.querySelectorAll('.stat-card .value');
                if (statCards.length >= 4) {
                    statCards[0].textContent = stats.todayOrders || 0;
                    statCards[1].textContent = stats.pendingOrders || 0;
                    statCards[2].textContent = `¥${(stats.todayRevenue || 0).toFixed(2)}`;
                    statCards[3].textContent = stats.totalDishes || 0;
                }
            }

            updateHotDishesDisplay(dishes) {
                // 这里可以更新热销菜品显示
                console.log('热销菜品:', dishes);
            }

            updateRecentOrdersDisplay(orders) {
                // 这里可以更新最近订单显示
                console.log('最近订单:', orders);
            }
        }

        // 全局应用实例
        let app;

        // 全局函数（保持向后兼容）
        function showPage(pageId) {
            if (app) app.showPage(pageId);
        }

        function showSection(sectionId) {
            if (app) app.showSection(sectionId);
        }

        function login() {
            if (app) app.login();
        }

        function register() {
            if (app) app.register();
        }

        function logout() {
            if (app) app.logout();
        }

        // 页面加载完成后初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            app = new HotelApp();
        });
    </script>
</body>
</html>