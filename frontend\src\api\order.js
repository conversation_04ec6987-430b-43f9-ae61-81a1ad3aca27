import request from '@/utils/request'

/**
 * 订单相关API
 */
export const orderApi = {
  /**
   * 分页查询订单列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页大小
   * @param {string} params.orderId - 订单号
   * @param {string} params.tableNumber - 桌号
   * @param {string} params.status - 状态
   * @param {string} params.startDate - 开始日期
   * @param {string} params.endDate - 结束日期
   */
  getOrderPage(params) {
    return request({
      url: '/orders',
      method: 'get',
      params,
    })
  },

  /**
   * 根据ID查询订单详情
   * @param {number} id - 订单ID
   */
  getOrderById(id) {
    return request({
      url: `/orders/${id}`,
      method: 'get',
    })
  },

  /**
   * 创建订单
   * @param {Object} data - 订单数据
   * @param {string} data.tableNumber - 桌号
   * @param {number} data.totalPrice - 总价格
   * @param {Array} data.items - 订单明细
   */
  createOrder(data) {
    return request({
      url: '/orders',
      method: 'post',
      data,
    })
  },

  /**
   * 更新订单状态
   * @param {number} id - 订单ID
   * @param {string} status - 新状态
   */
  updateOrderStatus(id, status) {
    return request({
      url: `/orders/${id}/status`,
      method: 'patch',
      data: { status },
    })
  },

  /**
   * 删除订单
   * @param {number} id - 订单ID
   */
  deleteOrder(id) {
    return request({
      url: `/orders/${id}`,
      method: 'delete',
    })
  },

  /**
   * 获取今日统计数据
   */
  getTodayStats() {
    return request({
      url: '/orders/today-stats',
      method: 'get',
    })
  },
}
