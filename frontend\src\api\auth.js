import request from '@/utils/request'

/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 用户登录
   * @param {Object} data - 登录数据
   * @param {string} data.username - 用户名
   * @param {string} data.password - 密码
   */
  login(data) {
    return request({
      url: '/auth/login',
      method: 'post',
      data,
    })
  },

  /**
   * 用户注册
   * @param {Object} data - 注册数据
   * @param {string} data.username - 用户名
   * @param {string} data.password - 密码
   * @param {string} data.name - 姓名
   * @param {string} data.role - 角色 (ADMIN|STAFF)
   */
  register(data) {
    return request({
      url: '/auth/register',
      method: 'post',
      data,
    })
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return request({
      url: '/auth/me',
      method: 'get',
    })
  },

  /**
   * 修改密码
   * @param {Object} data - 密码数据
   * @param {string} data.currentPassword - 当前密码
   * @param {string} data.newPassword - 新密码
   */
  changePassword(data) {
    return request({
      url: '/auth/change-password',
      method: 'post',
      data,
    })
  },

  /**
   * 用户登出
   */
  logout() {
    return request({
      url: '/auth/logout',
      method: 'post',
    })
  },
}
