<template>
  <div class="settings">
    <div class="page-header">
      <h2>系统设置</h2>
    </div>
    
    <el-card>
      <div class="coming-soon">
        <el-icon class="icon"><Setting /></el-icon>
        <h3>系统设置功能</h3>
        <p>此功能将在第二部分实现，敬请期待...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Setting } from '@element-plus/icons-vue'
</script>

<style scoped lang="scss">
.settings {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    h2 {
      margin: 0;
      color: #333;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 60px 20px;
    
    .icon {
      font-size: 4rem;
      color: #ccc;
      margin-bottom: 20px;
    }
    
    h3 {
      margin: 0 0 12px 0;
      color: #666;
    }
    
    p {
      margin: 0;
      color: #999;
    }
  }
}
</style>
