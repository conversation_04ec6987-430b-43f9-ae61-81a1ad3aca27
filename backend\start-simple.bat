@echo off
echo ================================
echo 酒店点单系统 - 简化测试启动
echo ================================
echo.

echo 检查Java版本...
java -version
echo.

echo 停止可能运行的服务...
taskkill /f /im java.exe 2>nul
echo.

echo 清理并编译项目...
mvn clean compile -q
echo.

echo 启动简化测试应用...
echo 访问地址: http://localhost:8080
echo 测试接口: http://localhost:8080/test/hello
echo 健康检查: http://localhost:8080/test/health
echo 系统信息: http://localhost:8080/test/info
echo CORS测试: http://localhost:8080/test/cors-test
echo.
echo 按 Ctrl+C 停止服务
echo.

mvn exec:java -Dexec.mainClass="com.hotel.SimpleTestApplication" -Dexec.args="--server.port=8080"

pause
