<template>
  <div class="main-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="sidebar">
        <div class="sidebar-header">
          <div class="logo">
            <el-icon class="logo-icon">
              <Restaurant />
            </el-icon>
            <span v-show="!appStore.sidebarCollapsed" class="logo-text">
              酒店点单系统
            </span>
          </div>
        </div>
        
        <el-menu
          :default-active="$route.path"
          :collapse="appStore.sidebarCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <template v-for="route in menuRoutes" :key="route.path">
            <el-menu-item
              v-if="!route.meta?.hidden && hasPermission(route)"
              :index="route.path"
            >
              <el-icon>
                <component :is="route.meta?.icon || 'Document'" />
              </el-icon>
              <template #title>{{ route.meta?.title }}</template>
            </el-menu-item>
          </template>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button
              text
              @click="appStore.toggleSidebar"
            >
              <el-icon size="20">
                <Expand v-if="appStore.sidebarCollapsed" />
                <Fold v-else />
              </el-icon>
            </el-button>
            
            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                v-for="item in breadcrumbs"
                :key="item.path"
                :to="item.path"
              >
                {{ item.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <!-- 主题切换 -->
            <el-button
              text
              @click="appStore.toggleTheme"
            >
              <el-icon size="18">
                <Sunny v-if="appStore.isDark" />
                <Moon v-else />
              </el-icon>
            </el-button>
            
            <!-- 用户菜单 -->
            <el-dropdown @command="handleUserCommand">
              <div class="user-info">
                <el-avatar :size="32" :src="authStore.user?.avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="username">{{ authStore.user?.name }}</span>
                <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item command="changePassword">
                    <el-icon><Lock /></el-icon>
                    修改密码
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
    
    <!-- 修改密码对话框 -->
    <ChangePasswordDialog
      v-model="showChangePasswordDialog"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import { ElMessageBox } from 'element-plus'
import {
  Restaurant,
  Expand,
  Fold,
  Sunny,
  Moon,
  User,
  ArrowDown,
  Lock,
  SwitchButton,
} from '@element-plus/icons-vue'
import ChangePasswordDialog from '@/components/ChangePasswordDialog.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

const showChangePasswordDialog = ref(false)

// 侧边栏宽度
const sidebarWidth = computed(() => {
  return appStore.sidebarCollapsed ? '64px' : '240px'
})

// 菜单路由
const menuRoutes = computed(() => {
  return router.getRoutes().find(r => r.path === '/')?.children || []
})

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    title: item.meta.title,
    path: item.path,
  }))
})

// 权限检查
const hasPermission = (route) => {
  if (!route.meta?.roles) return true
  return route.meta.roles.includes(authStore.user?.role)
}

// 处理用户菜单命令
const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      // TODO: 打开个人资料页面
      break
    case 'changePassword':
      showChangePasswordDialog.value = true
      break
    case 'logout':
      await ElMessageBox.confirm(
        '确定要退出登录吗？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
      authStore.logout()
      break
  }
}
</script>

<style scoped lang="scss">
.main-layout {
  height: 100vh;
}

.sidebar {
  background: #001529;
  transition: width 0.3s;
  
  .sidebar-header {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid #1f1f1f;
    
    .logo {
      display: flex;
      align-items: center;
      color: white;
      font-size: 18px;
      font-weight: 600;
      
      .logo-icon {
        font-size: 24px;
        margin-right: 12px;
        color: #1890ff;
      }
      
      .logo-text {
        white-space: nowrap;
      }
    }
  }
  
  .sidebar-menu {
    border: none;
    background: transparent;
    
    :deep(.el-menu-item) {
      color: rgba(255, 255, 255, 0.65);
      
      &:hover {
        background-color: #1890ff;
        color: white;
      }
      
      &.is-active {
        background-color: #1890ff;
        color: white;
      }
    }
  }
}

.header {
  background: white;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px;
      border-radius: 6px;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f5f5f5;
      }
      
      .username {
        font-size: 14px;
        color: #333;
      }
      
      .dropdown-icon {
        font-size: 12px;
        color: #999;
      }
    }
  }
}

.main-content {
  background: #f5f5f5;
  padding: 20px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .header {
    padding: 0 12px;
    
    .header-left {
      gap: 12px;
    }
    
    .header-right {
      gap: 12px;
      
      .user-info .username {
        display: none;
      }
    }
  }
  
  .main-content {
    padding: 12px;
  }
}
</style>
