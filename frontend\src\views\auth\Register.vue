<template>
  <div class="register-container gradient-bg">
    <div class="register-card">
      <div class="register-header">
        <el-icon class="register-icon">
          <UserFilled />
        </el-icon>
        <h2>创建新账户</h2>
        <p>加入我们的酒店点单系统</p>
      </div>
      
      <div class="register-body">
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          size="large"
          @submit.prevent="handleRegister"
        >
          <el-form-item prop="name">
            <el-input
              v-model="registerForm.name"
              placeholder="请输入您的姓名"
              prefix-icon="User"
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="创建用户名"
              prefix-icon="Avatar"
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="设置密码"
              prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="确认密码"
              prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="role">
            <el-select
              v-model="registerForm.role"
              placeholder="选择角色"
              style="width: 100%"
            >
              <el-option label="服务员" value="STAFF" />
              <el-option label="管理员" value="ADMIN" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              style="width: 100%"
              :loading="authStore.isLoading"
              @click="handleRegister"
            >
              <el-icon><Plus /></el-icon>
              注册
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="register-footer">
          已有账户？
          <router-link to="/login" class="login-link">
            立即登录
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { UserFilled, Plus } from '@element-plus/icons-vue'

const authStore = useAuthStore()
const router = useRouter()
const registerFormRef = ref()

// 注册表单数据
const registerForm = reactive({
  name: '',
  username: '',
  password: '',
  confirmPassword: '',
  role: 'STAFF',
})

// 自定义验证函数
const validateConfirmPassword = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const registerRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { max: 50, message: '姓名长度不能超过 50 个字符', trigger: 'blur' },
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 位', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' },
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' },
  ],
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    await registerFormRef.value.validate()
    
    const { confirmPassword, ...registerData } = registerForm
    await authStore.register(registerData)
    
    // 注册成功，跳转到登录页
    router.push('/login')
  } catch (error) {
    if (error.errors) {
      // 表单验证错误
      return
    }
    console.error('注册失败:', error)
  }
}
</script>

<style scoped lang="scss">
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

.register-card {
  width: 100%;
  max-width: 450px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.register-header {
  background: #2ecc71;
  color: white;
  text-align: center;
  padding: 40px 30px;
  
  .register-icon {
    font-size: 3rem;
    margin-bottom: 15px;
  }
  
  h2 {
    font-size: 1.8rem;
    margin-bottom: 8px;
    font-weight: 600;
  }
  
  p {
    opacity: 0.9;
    font-size: 0.95rem;
  }
}

.register-body {
  padding: 40px 30px;
}

.register-footer {
  text-align: center;
  margin-top: 20px;
  color: var(--gray);
  font-size: 0.9rem;
  
  .login-link {
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

@media (max-width: 768px) {
  .register-container {
    padding: 10px;
  }
  
  .register-header {
    padding: 30px 20px;
  }
  
  .register-body {
    padding: 30px 20px;
  }
}
</style>
