<template>
  <div class="test-page">
    <div class="container">
      <h1>系统测试页面</h1>
      
      <el-card class="test-card">
        <h3>前端状态检查</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="Vue版本">{{ vueVersion }}</el-descriptions-item>
          <el-descriptions-item label="Element Plus">已加载</el-descriptions-item>
          <el-descriptions-item label="路由状态">正常</el-descriptions-item>
          <el-descriptions-item label="当前时间">{{ currentTime }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <el-card class="test-card">
        <h3>后端连接测试</h3>
        <div class="test-actions">
          <el-button type="primary" @click="testBackendConnection" :loading="testing">
            <el-icon><Connection /></el-icon>
            测试后端连接
          </el-button>
          <el-button type="success" @click="testHealthCheck" :loading="healthTesting">
            <el-icon><CircleCheck /></el-icon>
            健康检查
          </el-button>
        </div>
        
        <div v-if="testResult" class="test-result">
          <el-alert
            :title="testResult.title"
            :type="testResult.type"
            :description="testResult.message"
            show-icon
            :closable="false"
          />
        </div>
      </el-card>
      
      <el-card class="test-card">
        <h3>快速导航</h3>
        <div class="nav-buttons">
          <el-button @click="$router.push('/login')">
            <el-icon><User /></el-icon>
            登录页面
          </el-button>
          <el-button @click="$router.push('/register')">
            <el-icon><UserFilled /></el-icon>
            注册页面
          </el-button>
          <el-button @click="$router.push('/')">
            <el-icon><House /></el-icon>
            主页面
          </el-button>
        </div>
      </el-card>
      
      <el-card class="test-card">
        <h3>系统信息</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="项目名称">酒店点单系统</el-descriptions-item>
          <el-descriptions-item label="前端框架">Vue 3 + Element Plus</el-descriptions-item>
          <el-descriptions-item label="后端框架">Spring Boot</el-descriptions-item>
          <el-descriptions-item label="开发状态">开发中</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { version } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Connection,
  CircleCheck,
  User,
  UserFilled,
  House,
} from '@element-plus/icons-vue'
import request from '@/utils/request'

const vueVersion = version
const currentTime = ref('')
const testing = ref(false)
const healthTesting = ref(false)
const testResult = ref(null)

// 更新当前时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 测试后端连接
const testBackendConnection = async () => {
  testing.value = true
  testResult.value = null
  
  try {
    const response = await request({
      url: '/test/hello',
      method: 'get',
    })
    
    testResult.value = {
      title: '连接成功',
      type: 'success',
      message: `后端响应: ${response.data || '连接正常'}`
    }
    ElMessage.success('后端连接测试成功')
  } catch (error) {
    console.error('后端连接测试失败:', error)
    testResult.value = {
      title: '连接失败',
      type: 'error',
      message: `错误信息: ${error.message || '无法连接到后端服务'}`
    }
    ElMessage.error('后端连接测试失败')
  } finally {
    testing.value = false
  }
}

// 健康检查
const testHealthCheck = async () => {
  healthTesting.value = true
  
  try {
    const response = await request({
      url: '/test/health',
      method: 'get',
    })
    
    testResult.value = {
      title: '健康检查通过',
      type: 'success',
      message: `服务状态: ${response.data || 'OK'}`
    }
    ElMessage.success('健康检查通过')
  } catch (error) {
    console.error('健康检查失败:', error)
    testResult.value = {
      title: '健康检查失败',
      type: 'error',
      message: `错误信息: ${error.message || '服务不可用'}`
    }
    ElMessage.error('健康检查失败')
  } finally {
    healthTesting.value = false
  }
}

onMounted(() => {
  updateTime()
  // 每秒更新时间
  setInterval(updateTime, 1000)
})
</script>

<style scoped lang="scss">
.test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  
  .container {
    max-width: 800px;
    margin: 0 auto;
    
    h1 {
      text-align: center;
      color: #333;
      margin-bottom: 30px;
      font-size: 2.5rem;
    }
  }
  
  .test-card {
    margin-bottom: 20px;
    
    h3 {
      margin: 0 0 20px 0;
      color: #333;
      border-bottom: 2px solid #409eff;
      padding-bottom: 10px;
    }
  }
  
  .test-actions {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }
  
  .test-result {
    margin-top: 20px;
  }
  
  .nav-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .test-page {
    padding: 10px;
    
    .container h1 {
      font-size: 2rem;
    }
    
    .test-actions,
    .nav-buttons {
      flex-direction: column;
      
      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
