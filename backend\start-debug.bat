@echo off
echo ================================
echo 酒店点单系统 - 后端调试启动
echo ================================
echo.

echo 检查Java版本...
java -version
echo.

echo 检查Maven版本...
mvn --version
echo.

echo 编译项目...
mvn clean compile
echo.

echo 启动Spring Boot应用 (开发模式)...
echo 访问地址: http://localhost:8080
echo 测试接口: http://localhost:8080/test/hello
echo H2控制台: http://localhost:8080/h2-console
echo.
mvn spring-boot:run -Dspring-boot.run.profiles=dev

pause
