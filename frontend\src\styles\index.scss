// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

// CSS变量定义
:root {
  --primary: #3498db;
  --primary-dark: #2980b9;
  --secondary: #e74c3c;
  --success: #2ecc71;
  --warning: #f39c12;
  --light: #f8f9fa;
  --dark: #343a40;
  --gray: #6c757d;
  --light-gray: #e9ecef;
  --border: #dee2e6;
}

// 背景渐变
.gradient-bg {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

// 卡片样式
.card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
  }
}

// 按钮样式
.btn {
  padding: 8px 20px;
  border: none;
  border-radius: 5px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  
  &.btn-primary {
    background: var(--primary);
    color: white;
    
    &:hover {
      background: var(--primary-dark);
    }
  }
  
  &.btn-outline {
    background: transparent;
    border: 1px solid var(--primary);
    color: var(--primary);
    
    &:hover {
      background: var(--primary);
      color: white;
    }
  }
  
  &.btn-danger {
    background: var(--secondary);
    color: white;
    
    &:hover {
      background: #c0392b;
    }
  }
}

// 状态标签样式
.status {
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  
  &.status-pending {
    background: #ffeeba;
    color: #856404;
  }
  
  &.status-preparing {
    background: #b8daff;
    color: #004085;
  }
  
  &.status-completed {
    background: #c3e6cb;
    color: #155724;
  }
  
  &.status-cancelled {
    background: #f5c6cb;
    color: #721c24;
  }
}

// 分类标签样式
.category {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  
  &.category-main {
    background: #d4edda;
    color: #155724;
  }
  
  &.category-drink {
    background: #cce5ff;
    color: #004085;
  }
  
  &.category-dessert {
    background: #fff3cd;
    color: #856404;
  }
  
  &.category-appetizer {
    background: #f8d7da;
    color: #721c24;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .card {
    margin: 10px;
  }
}

// Element Plus 样式覆盖
.el-card {
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.el-button {
  border-radius: 5px;
}

.el-input {
  .el-input__wrapper {
    border-radius: 5px;
  }
}

.el-table {
  border-radius: 10px;
  overflow: hidden;
}
