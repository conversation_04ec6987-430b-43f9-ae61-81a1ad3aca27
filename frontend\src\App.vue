<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

onMounted(async () => {
  try {
    // 应用启动时检查登录状态
    await authStore.checkAuth()
  } catch (error) {
    console.error('检查认证状态失败:', error)
    // 即使认证检查失败，也不阻止应用启动
  }
})
</script>

<style>
#app {
  height: 100vh;
  width: 100vw;
}
</style>
